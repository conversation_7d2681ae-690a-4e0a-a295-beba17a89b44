package com.tqhit.battery.one.service

import android.app.Service
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.BatteryManager
import android.os.IBinder
import android.util.Log
import androidx.core.content.ContextCompat
import com.tqhit.battery.one.activity.overlay.ChargingOverlayActivity
import com.tqhit.battery.one.repository.AnimationRepository
import com.tqhit.battery.one.repository.AppRepository
import com.tqhit.battery.one.utils.NotificationUtils
import dagger.hilt.android.AndroidEntryPoint
import java.io.File
import javax.inject.Inject

@AndroidEntryPoint
class ChargingOverlayService : Service() {
    @Inject lateinit var appRepository: AppRepository
    @Inject lateinit var animationRepository: AnimationRepository
    private var isCharging = false
    private var videoPath: String? = null

    private val batteryReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent?) {
            val status = intent?.getIntExtra(BatteryManager.EXTRA_STATUS, -1) ?: -1
            val charging = status == BatteryManager.BATTERY_STATUS_CHARGING || status == BatteryManager.BATTERY_STATUS_FULL
            if (charging && !isCharging) {
                isCharging = true
                startOverlayActivity(context, intent)
            } else if (!charging && isCharging) {
                isCharging = false
            }
        }
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        NotificationUtils.createNotificationChannel(this)
        startForeground(NOTIFICATION_ID, NotificationUtils.createChargingOverlayNotification(this))
        registerReceiver(batteryReceiver, IntentFilter(Intent.ACTION_BATTERY_CHANGED))
        // Check current state immediately
        val batteryStatus = registerReceiver(null, IntentFilter(Intent.ACTION_BATTERY_CHANGED))
        val status = batteryStatus?.getIntExtra(BatteryManager.EXTRA_STATUS, -1) ?: -1
        isCharging = status == BatteryManager.BATTERY_STATUS_CHARGING || status == BatteryManager.BATTERY_STATUS_FULL
        return START_STICKY
    }

    private fun startOverlayActivity(context: Context, intent: Intent?) {
        if (!appRepository.isAnimationOverlayEnabled() || animationRepository.getTrialEndTime() <= System.currentTimeMillis()) return
        videoPath = determineVideoSource()
        if (videoPath.isNullOrEmpty()) return
        val overlayIntent = Intent(this, ChargingOverlayActivity::class.java).apply {
            addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            putExtra("extra_video_path", videoPath)
        }
        context.startActivity(overlayIntent)
    }

    /**
     * Determines the best video source using the same priority logic as ChargingOverlayActivity
     */
    private fun determineVideoSource(): String? {
        // First priority: Check if permanent file exists
        val permanentFilePath = appRepository.getVideoPath()
        if (!permanentFilePath.isNullOrEmpty()) {
            val file = File(permanentFilePath)
            if (file.exists() && file.length() > 0) {
                return permanentFilePath
            }
        }

        // Second priority: Fall back to applied animation URL for immediate playback
        val appliedAnimationUrl = appRepository.getAppliedAnimationUrl()
        if (!appliedAnimationUrl.isNullOrEmpty()) {
            return appliedAnimationUrl
        }

        return null
    }

    override fun onDestroy() {
        super.onDestroy()
        unregisterReceiver(batteryReceiver)
    }

    override fun onBind(intent: Intent?): IBinder? = null

    companion object {
        private const val NOTIFICATION_ID = 1001
    }
}