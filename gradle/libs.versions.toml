[versions]
adlib = "1.2.8"
agp = "8.9.0"
applovin = "13.2.0.1"
blurview = "version-2.0.6"
circularseekbar = "1.4.2"
coreSplashscreen = "1.0.1"
dotsindicator = "5.1.0"
facebook = "6.19.0.1"
glide = "4.16.0"
gson = "2.12.1"
hiltAndroid = "2.56.1"
inmobi = "10.8.2.0"
ironsource = "8.8.0.0"
kotlin = "2.1.0"
coreKtx = "1.16.0"
junit = "4.13.2"
junitVersion = "1.2.1"
espressoCore = "3.6.1"
appcompat = "1.7.0"
lottie = "6.6.2"
material = "1.12.0"
media3Ui = "1.6.1"
media3Exoplayer = "1.6.1"
mintegral = "16.9.71.0"
mpandroidchart = "v3.1.0"
navigationUiKtx = "2.8.9"
navigationFragmentKtx = "2.8.9"
pangle = "6.5.0.9.0"
sdpAndroid = "1.1.1"
shimmer = "0.5.0"
playServicesAds = "24.2.0"
sspAndroid = "1.1.1"
unity = "4.14.2.0"
unityAds = "4.14.2"
vungle = "7.5.0.0"
activity = "1.10.1"
constraintlayout = "2.2.1"
firebaseAnalytics = "22.4.0"
firebaseBom = "33.7.0"

[libraries]
adlib = { module = "com.github.tqhit:AdLib", version.ref = "adlib" }
androidx-core-ktx = { group = "androidx.core", name = "core-ktx", version.ref = "coreKtx" }
androidx-core-splashscreen = { module = "androidx.core:core-splashscreen", version.ref = "coreSplashscreen" }
androidx-media3-exoplayer = { module = "androidx.media3:media3-exoplayer", version.ref = "media3Exoplayer" }
androidx-media3-ui = { module = "androidx.media3:media3-ui", version.ref = "media3Ui" }
androidx-navigation-fragment-ktx = { module = "androidx.navigation:navigation-fragment-ktx", version.ref = "navigationFragmentKtx" }
androidx-navigation-ui-ktx = { module = "androidx.navigation:navigation-ui-ktx", version.ref = "navigationUiKtx" }
applovin = { module = "com.google.ads.mediation:applovin", version.ref = "applovin" }
blurview = { module = "com.github.Dimezis:BlurView", version.ref = "blurview" }
circularseekbar = { module = "me.tankery.lib:circularSeekBar", version.ref = "circularseekbar" }
dotsindicator = { module = "com.tbuonomo:dotsindicator", version.ref = "dotsindicator" }
facebook = { module = "com.google.ads.mediation:facebook", version.ref = "facebook" }
glide = { module = "com.github.bumptech.glide:glide", version.ref = "glide" }
gson = { module = "com.google.code.gson:gson", version.ref = "gson" }
hilt-android = { module = "com.google.dagger:hilt-android", version.ref = "hiltAndroid" }
hilt-android-compiler = { module = "com.google.dagger:hilt-android-compiler", version.ref = "hiltAndroid" }
inmobi = { module = "com.google.ads.mediation:inmobi", version.ref = "inmobi" }
ironsource = { module = "com.google.ads.mediation:ironsource", version.ref = "ironsource" }
junit = { group = "junit", name = "junit", version.ref = "junit" }
androidx-junit = { group = "androidx.test.ext", name = "junit", version.ref = "junitVersion" }
androidx-espresso-core = { group = "androidx.test.espresso", name = "espresso-core", version.ref = "espressoCore" }
androidx-appcompat = { group = "androidx.appcompat", name = "appcompat", version.ref = "appcompat" }
lottie = { module = "com.airbnb.android:lottie", version.ref = "lottie" }
material = { group = "com.google.android.material", name = "material", version.ref = "material" }
mintegral = { module = "com.google.ads.mediation:mintegral", version.ref = "mintegral" }
mpandroidchart = { module = "com.github.PhilJay:MPAndroidChart", version.ref = "mpandroidchart" }
pangle = { module = "com.google.ads.mediation:pangle", version.ref = "pangle" }
play-services-ads = { module = "com.google.android.gms:play-services-ads", version.ref = "playServicesAds" }
sdp-android = { module = "com.intuit.sdp:sdp-android", version.ref = "sdpAndroid" }
shimmer = { module = "com.facebook.shimmer:shimmer", version.ref = "shimmer" }
ssp-android = { module = "com.intuit.ssp:ssp-android", version.ref = "sspAndroid" }
unity = { module = "com.google.ads.mediation:unity", version.ref = "unity" }
unity-ads = { module = "com.unity3d.ads:unity-ads", version.ref = "unityAds" }
vungle = { module = "com.google.ads.mediation:vungle", version.ref = "vungle" }
androidx-activity = { group = "androidx.activity", name = "activity", version.ref = "activity" }
androidx-constraintlayout = { group = "androidx.constraintlayout", name = "constraintlayout", version.ref = "constraintlayout" }
firebase-analytics = { group = "com.google.firebase", name = "firebase-analytics", version.ref = "firebaseAnalytics" }
firebase-remote-config = { group = "com.google.firebase", name = "firebase-config" }
firebase-bom = { group = "com.google.firebase", name = "firebase-bom", version.ref = "firebaseBom" }

[plugins]
android-application = { id = "com.android.application", version.ref = "agp" }
kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }

